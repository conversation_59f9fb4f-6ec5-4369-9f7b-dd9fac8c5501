import { createApp } from "vue";
import { createRouter, createWebHistory } from "vue-router";
import App from "./App.vue";
import "normalize.css";

// 导入页面组件
import Home from "./views/Home.vue";
import ChinaMap from "./views/ChinaMap.vue";

// 创建路由配置
const routes = [
  { path: "/", component: Home },
  { path: "/china-map", component: ChinaMap },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

const app = createApp(App);
app.use(router);
app.mount("#app");
