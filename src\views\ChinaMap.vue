<template>
  <div class="china-map">
    <h1>中国地图页面</h1>
    <div class="nav-buttons">
      <router-link to="/" class="nav-btn">返回3D地图</router-link>
      <router-link to="/test" class="nav-btn">测试页面</router-link>
    </div>

    <div class="debug-info">
      <p>页面状态: {{ loading ? "加载中" : error ? "错误" : "正常" }}</p>
      <p v-if="error">错误信息: {{ error }}</p>
      <p>图表容器: {{ chartRef ? "已创建" : "未创建" }}</p>
    </div>

    <div v-if="loading" class="loading">
      <p>正在加载地图数据...</p>
    </div>
    <div v-if="error" class="error">
      <p>{{ error }}</p>
      <button class="retry-btn" @click="retryLoad">重试</button>
    </div>
    <div v-show="!loading && !error" ref="chartRef" class="map-chart"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";

const chartRef = ref<HTMLDivElement>();
const loading = ref(true);
const error = ref("");

// 图标数据
const iconRQ =
  "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAUCAYAAABiS3YzAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjNCRkRBMEJBQzgwRjExRUFBNUI0RTMyMThEN0UxMzFEIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjNCRkRBMEJCQzgwRjExRUFBNUI0RTMyMThEN0UxMzFEIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6M0JGREEwQjhDODBGMTFFQUE1QjRFMzIxOEQ3RTEzMUQiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6M0JGREEwQjlDODBGMTFFQUE1QjRFMzIxOEQ3RTEzMUQiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5mT42iAAABQ0lEQVR42mL8LabOQCQIBOL1xChkItJAkLp+IBajpqFWQCwPxJ7UNDQCSgdQy1BmIA6Bsl2AmJMahjoAsTiUzQPETtQwNAKN709IAwvUayZQ/hcg/o0k/x6Ig9D0+ABxKJT9HYh/oMm/BBm6GYitgTgfiBmJcLkkEK/CIXcGiGNB3v8JxIVQF31gIA/8AeIWaNK7gRymG4BYH4hPkGjgXSC2A+JaWNChR9QjqIJeIP5PhIGzgdgAiI8Tin2QbSVAvIOAgROBOA0auUQlKV4gtidgqBGp6RSUFrmIKA/ESDEUPcGfBOIUIH6Lln29iTVUCIjdkJJKExDbAPFcqJdPEMpd2AwF5TBWaFKxBeJ6qOHIqaMbmjrcsBUw2AwNh7rKAEeaBaWOMiD2BeJvQOxOyFBuaFJJwZZU0MBWaHCIo0sABBgAetA4Jx5t/ToAAAAASUVORK5CYII=";

// 散点数据
const data = [
  {
    name: "北京",
    value: [116.24, 39.55, 100],
  },
  {
    name: "深圳",
    value: [114.271522, 22.753644],
  },
  {
    name: "重庆",
    value: [106.54, 29.59],
  },
  {
    name: "浙江",
    value: [120.19, 30.26],
  },
];

// 标签线数据
const LableData = [
  {
    name: "北京",
    coords: [
      [116.24, 39.55, 100],
      [120.24, 46.55, 100],
    ],
    value: [10.21, 1.2],
  },
  {
    name: "深圳",
    coords: [
      [114.271522, 22.753644],
      [118.24, 18.55, 100],
    ],
    value: [10.21, 1.2],
  },
  {
    name: "重庆",
    coords: [
      [106.54, 29.59],
      [100.24, 40.55],
    ],
    value: [10.21, 1.2],
  },
  {
    name: "浙江",
    coords: [
      [120.19, 30.26],
      [128.24, 35.55, 100],
    ],
    value: [10.21, 1.2],
  },
];

// 地图配置选项
const option = {
  backgroundColor: "#000f1e",
  geo: {
    map: "china",
    aspectScale: 0.85,
    layoutCenter: ["50%", "50%"],
    layoutSize: "100%",
    itemStyle: {
      normal: {
        shadowColor: "#276fce",
        shadowOffsetX: 0,
        shadowOffsetY: 15,
        opacity: 0.5,
      },
      emphasis: {
        areaColor: "#276fce",
      },
    },
    regions: [
      {
        name: "南海诸岛",
        itemStyle: {
          areaColor: "rgba(0, 10, 52, 1)",
          borderColor: "rgba(0, 10, 52, 1)",
          normal: {
            opacity: 0,
            label: {
              show: false,
              color: "#009cc9",
            },
          },
        },
        label: {
          show: false,
          color: "#FFFFFF",
          fontSize: 12,
        },
      },
    ],
  },
  series: [
    // 常规地图
    {
      type: "map",
      mapType: "china",
      aspectScale: 0.85,
      layoutCenter: ["50%", "50%"],
      layoutSize: "100%",
      zoom: 1,
      scaleLimit: {
        min: 1,
        max: 2,
      },
      itemStyle: {
        normal: {
          areaColor: "#0c274b",
          borderColor: "#1cccff",
          borderWidth: 1.5,
        },
        emphasis: {
          areaColor: "#02102b",
          label: {
            color: "#fff",
          },
        },
      },
    },
    // 首都星图标
    {
      name: "首都",
      type: "scatter",
      coordinateSystem: "geo",
      data: [
        {
          name: "首都",
          value: [116.24, 41.55, 100],
        },
      ],
      symbol: iconRQ,
      symbolSize: 20,
      label: {
        normal: {
          show: false,
        },
        emphasis: {
          show: false,
        },
      },
    },
    // 区域散点图
    {
      type: "effectScatter",
      coordinateSystem: "geo",
      zlevel: 2,
      symbolSize: 10,
      rippleEffect: {
        period: 3,
        scale: 5,
        brushType: "fill",
      },
      label: {
        normal: {
          show: true,
          position: "right",
          formatter: "{b}",
          color: "#b3e2f2",
          fontWeight: "bold",
          fontSize: 18,
        },
      },
      data,
      itemStyle: {
        normal: {
          show: true,
          color: "green",
          shadowBlur: 20,
          shadowColor: "#fff",
        },
        emphasis: {
          areaColor: "#f00",
        },
      },
    },
    // 线条和点
    {
      type: "lines",
      zlevel: 1,
      polyline: true,
      effect: {
        show: true,
        period: 10,
        trailLength: 0.7,
        color: "#fff",
        symbol: "arrow",
        symbolSize: 6,
      },
      lineStyle: {
        normal: {
          color: "#fff",
          width: 1.5,
          curveness: 0.2,
          shadowColor: "#fff",
        },
      },
      data: [
        {
          fromName: "深圳",
          toName: "北京",
          coords: [
            [114.271522, 22.753644],
            [116.24, 39.55],
            [114.271522, 22.753644],
          ],
        },
        {
          fromName: "深圳",
          toName: "浙江",
          coords: [
            [114.271522, 22.753644],
            [120.19, 30.26],
            [114.271522, 22.753644],
          ],
        },
        {
          fromName: "深圳",
          toName: "重庆",
          coords: [
            [114.271522, 22.753644],
            [106.54, 29.59],
            [114.271522, 22.753644],
          ],
        },
      ],
    },
    // 标签线
    {
      type: "lines",
      zlevel: 3,
      symbol: "circle",
      symbolSize: [5, 5],
      color: "#ff8003",
      opacity: 1,
      label: {
        show: true,
        padding: [10, 20],
        color: "#fff",
        backgroundColor: "#1a3961",
        borderColor: "#aee9fb",
        borderWidth: 1,
        borderRadius: 6,
        formatter(params: any) {
          console.log(params);
          const arr = [params.name, "上行：" + params.value[1] + "G/s", "下行：" + params.value[0] + "G/s"];
          return arr.join("\n");
        },
        textStyle: {
          align: "left",
          lineHeight: 20,
        },
      },
      lineStyle: {
        type: "solid",
        color: "#fff",
        width: 0.5,
        opacity: 1,
      },
      data: LableData,
    },
  ],
};

const loadMap = async () => {
  loading.value = true;
  error.value = "";

  try {
    console.log("开始加载地图数据...");

    // 使用更可靠的地图数据源
    const response = await fetch("https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json");

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const chinaGeoJson = await response.json();
    console.log("地图数据加载成功:", chinaGeoJson);

    echarts.registerMap("china", chinaGeoJson);

    if (!chartRef.value) {
      throw new Error("图表容器未找到");
    }

    // 创建图表实例
    const myChart = echarts.init(chartRef.value);
    console.log("图表实例创建成功");

    // 设置配置选项
    myChart.setOption(option);
    console.log("图表配置设置成功");

    // 绑定点击事件
    myChart.on("click", function (params: any) {
      console.log("地图点击事件:", params);
    });

    // 窗口大小变化时重新调整图表
    window.addEventListener("resize", () => {
      myChart.resize();
    });

    loading.value = false;
    console.log("地图加载完成");
  } catch (err: any) {
    console.error("加载中国地图数据失败:", err);
    error.value = `地图数据加载失败: ${err.message}`;
    loading.value = false;
  }
};

const retryLoad = () => {
  loadMap();
};

onMounted(() => {
  loadMap();
});
</script>

<style lang="less" scoped>
.china-map {
  width: 100%;
  height: 100vh;
  position: relative;
  background: #000f1e;

  h1 {
    color: white;
    text-align: center;
    padding: 20px;
    margin: 0;
    font-size: 24px;
  }
}

.nav-buttons {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.debug-info {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.8);
  padding: 10px;
  border-radius: 8px;
  color: white;
  font-size: 14px;
}

.nav-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #1cccff, #3fbcce);
  color: #000;
  text-decoration: none;
  border-radius: 8px;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 0 20px rgba(28, 204, 255, 0.4);

  &:hover {
    background: linear-gradient(135deg, #fff, #1cccff);
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(28, 204, 255, 0.6);
  }
}

.map-chart {
  width: 100%;
  height: 100%;
}

.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: white;
  font-size: 18px;
  flex-direction: column;
}

.retry-btn {
  margin-top: 20px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #1cccff, #3fbcce);
  color: #000;
  border: none;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #fff, #1cccff);
    transform: translateY(-2px);
  }
}
</style>
